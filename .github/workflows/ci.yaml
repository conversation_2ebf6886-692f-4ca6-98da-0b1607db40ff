# Copyright 2025 Google LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: CI

on:
  push:
    branches: ["main"]
  pull_request:
    branches: ["main"]

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11"]
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e ".[dev,test]"

      - name: Run unit tests and linting
        run: |
          PY_VERSION=$(echo "${{ matrix.python-version }}" | tr -d '.')
          tox -e py${PY_VERSION},format,lint-src,lint-tests

  live-api-tests:
    needs: test
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'push' ||
      (github.event_name == 'pull_request' &&
       github.event.pull_request.head.repo.full_name == github.repository)

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e ".[dev,test]"

      - name: Run live API tests
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          LANGEXTRACT_API_KEY: ${{ secrets.GEMINI_API_KEY }}  # For backward compatibility
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        run: |
          if [[ -z "$GEMINI_API_KEY" && -z "$OPENAI_API_KEY" ]]; then
            echo "::notice::Live API tests skipped - no provider secrets configured"
            exit 0
          fi
          tox -e live-api

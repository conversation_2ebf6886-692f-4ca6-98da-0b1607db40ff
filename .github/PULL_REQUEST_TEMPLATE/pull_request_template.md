# Description

Replace this with a clear and concise change description

<!--- Important: All PRs must be linked to at least one issue (except for
  extremely trivial and straightforward changes). --->

<!--- This issue (or issues) should document the motivation, context,
  alternatives considered, risks (such as breaking backwards compatibility), and
  any new dependencies. --->

Fixes #[issue number]

Choose one: (Bug fix | Feature | Documentation | Testing | Code health | Other)

# How Has This Been Tested?

Replace this with a description of the tests that you ran to verify your
changes. If executing the existing test suite without customization, simply
paste the command line used.

```
$ python -m unittest discover ...
```

# Checklist:

<!--- Put an `x` in the box if you did the task -->

<!--- If you forgot a task please follow the instructions below -->

-   [ ] I have read and acknowledged Google's Open Source
    [Code of conduct](https://opensource.google/conduct).
-   [ ] I have read the
    [Contributing](https://github.com/google-health/langextract/blob/master/CONTRIBUTING.md)
    page, and I either signed the Google
    [Individual CLA](https://cla.developers.google.com/about/google-individual)
    or am covered by my company's
    [Corporate CLA](https://cla.developers.google.com/about/google-corporate).
-   [ ] I have discussed my proposed solution with code owners in the linked
    issue(s) and we have agreed upon the general approach.
-   [ ] I have made any needed documentation changes, or noted in the linked
    issue(s) that documentation elsewhere needs updating.
-   [ ] I have added tests, or I have ensured existing tests cover the changes
-   [ ] I have followed
    [Google's Python Style Guide](https://google.github.io/styleguide/pyguide.html)
    and ran `pylint` over the affected code.

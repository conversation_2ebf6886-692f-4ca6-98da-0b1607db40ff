# Copyright 2025 Google LLC.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Byte-compiled / Cache files
__pycache__/
*.py[cod]
*$py.class

# Distribution / Packaging
build/
dist/
*.egg-info/
.eggs/
eggs/

# Virtual Environments
.env
.venv
env/
venv/
ENV/

# Test & Coverage Reports
.pytest_cache/
.tox/
htmlcov/
.coverage
.coverage.*

# Generated Output & Data
# LangExtract outputs are defaulted to test_output/
/test_output/

# Sphinx documentation build output
docs/_build/

# IDE / Editor specific
.idea/
.vscode/
*.swp

# OS-specific
.DS_Store

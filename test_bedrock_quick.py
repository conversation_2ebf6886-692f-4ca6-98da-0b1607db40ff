#!/usr/bin/env python3

import os
import sys
import subprocess

# Add the current directory to Python path
sys.path.insert(0, '/Users/<USER>/Downloads/langextract')

def run_bedrock_tests():
    """Run just the Bedrock tests to check if they pass."""
    try:
        result = subprocess.run([
            'python', '-m', 'pytest', 
            'tests/inference_test.py::TestBedrockConverseLanguageModel', 
            '-v'
        ], 
        cwd='/Users/<USER>/Downloads/langextract',
        capture_output=True, 
        text=True,
        timeout=60
        )
        
        print("STDOUT:")
        print(result.stdout)
        print("\nSTDERR:")
        print(result.stderr)
        print(f"\nReturn code: {result.returncode}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("Test timed out!")
        return False
    except Exception as e:
        print(f"Error running tests: {e}")
        return False

if __name__ == "__main__":
    success = run_bedrock_tests()
    if success:
        print("\n✅ All Bedrock tests passed!")
    else:
        print("\n❌ Some Bedrock tests failed!")
    sys.exit(0 if success else 1)
